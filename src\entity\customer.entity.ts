import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  BeforeCreate,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { generateUniqueCode } from '../common/Utils';
import { Pet } from './pet.entity';
import { Order } from './order.entity';
import { Complaint } from './complaint.entity';
import { CustomerAddress } from './address.entity';
import { Review } from './review.entity';
import { Employee } from './employee.entity';

export interface CustomerAttributes {
  /** 客户ID */
  id: number;
  /** 微信openid */
  openid: string;
  /** 手机号 */
  phone: string;
  /** 昵称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 性别：0-未知 1-男 2-女 */
  gender?: number;
  /** 详细地址 */
  address?: string;
  /** 会员状态：0-普通会员 1-权益会员 */
  memberStatus: number;
  /** 积分值 */
  points: number;
  /** 最后登录时间 */
  lastLoginTime?: Date;
  /** 关联的宠物列表 */
  pets?: Pet[];
  /** 关联的订单列表 */
  orders?: Order[];
  /** 状态：0-禁用 1-启用 */
  status: number;
  /** 推广码 */
  promotionCode: string;
  /** 推广员工ID */
  promotionEmployeeId?: number;
  /** 推广员工信息 */
  promotionEmployee?: Employee;
  addresses?: CustomerAddress[];
  /** 关联的评价列表 */
  reviews?: Review[];
}

@Table({ tableName: 'customers', timestamps: true, comment: '客户表' })
export class Customer
  extends Model<CustomerAttributes>
  implements CustomerAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '客户ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    unique: {
      name: 'openid',
      msg: '已存在相同的openid',
    },
    allowNull: false,
    comment: '微信openid',
  })
  openid: string;

  @Column({
    type: DataType.STRING(20),
    unique: {
      name: 'phone',
      msg: '已存在相同的手机号',
    },
    allowNull: false,
    comment: '手机号',
  })
  phone: string;

  @Column({
    type: DataType.STRING(20),
    unique: {
      name: 'promotionCode',
      msg: '推广码已存在',
    },
    allowNull: false,
    comment: '推广码',
  })
  promotionCode: string;

  @Column({
    type: DataType.STRING(50),
    comment: '昵称',
  })
  nickname: string;

  @Column({
    type: DataType.STRING(200),
    comment: '头像',
  })
  avatar: string;

  @Column({
    type: DataType.TINYINT,
    comment: '性别：0-女 1-男',
  })
  gender: number;

  @Column({
    type: DataType.STRING(200),
    comment: '详细地址',
  })
  address: string;

  @Column({
    type: DataType.TINYINT,
    defaultValue: 0,
    comment: '会员状态：0-普通会员 1-权益会员',
  })
  memberStatus: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '积分值',
  })
  points: number;

  @Column({
    type: DataType.DATE,
    comment: '最后登录时间',
  })
  lastLoginTime: Date;

  @Column({
    type: DataType.TINYINT,
    defaultValue: 1,
    comment: '状态：0-禁用 1-启用',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    comment: '推广员工ID',
  })
  @ForeignKey(() => Employee)
  promotionEmployeeId: number;

  @BelongsTo(() => Employee, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  promotionEmployee: Employee;

  @HasMany(() => Pet)
  pets: Pet[];

  @HasMany(() => Order)
  orders: Order[];

  @HasMany(() => Complaint)
  complaints: Complaint[];

  @HasMany(() => CustomerAddress)
  addresses: CustomerAddress[];

  @HasMany(() => Review)
  reviews: Review[];

  @BeforeCreate
  static async generateUniqueCode(instance: Customer) {
    if (!instance.promotionCode) {
      instance.promotionCode = generateUniqueCode();
    }
  }
}
