import { <PERSON>, Get, Param, Query, Inject } from '@midwayjs/core';
import { MessageStatisticsService } from '../service/message-statistics.service';

@Controller('/api/message-statistics')
export class MessageStatisticsController {
  @Inject()
  messageStatisticsService: MessageStatisticsService;

  @Get('/overview')
  async getOverview() {
    return await this.messageStatisticsService.getMessageOverview();
  }

  @Get('/user/:userId')
  async getUserStatistics(@Param('userId') userId: number) {
    return await this.messageStatisticsService.getUserMessageStatistics(userId);
  }

  @Get('/template-usage')
  async getTemplateUsage() {
    return await this.messageStatisticsService.getTemplateUsageStatistics();
  }

  @Get('/read-rate')
  async getReadRate(@Query('days') days = 7) {
    return await this.messageStatisticsService.getReadRateStatistics(days);
  }
}
