import { Inject, Provide } from '@midwayjs/core';
import { EmployeePromotion, Employee, Customer } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Provide()
export class EmployeePromotionService extends BaseService<EmployeePromotion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('员工推广关系');
  }

  getModel() {
    return EmployeePromotion;
  }

  /**
   * 用户填写推广码建立推广关系
   * @param customerId 客户ID
   * @param promotionCode 推广码
   */
  async createPromotionRelation(customerId: number, promotionCode: string) {
    // 检查客户是否已经有推广关系
    const existingRelation = await this.findOne({
      where: { customerId },
    });
    if (existingRelation) {
      throw new CustomError('您已经填写过推广码，不能重复填写');
    }

    // 查找推广码对应的员工
    const employee = await Employee.findOne({
      where: { promotionCode },
    });
    if (!employee) {
      throw new CustomError('推广码不存在');
    }

    // 检查客户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('客户不存在');
    }

    // 不能推广自己（如果客户也是员工的话，这里可以根据业务需求调整）
    // 创建推广关系
    const promotion = await this.create({
      employeeId: employee.id,
      customerId,
      promotionCode,
      promotionTime: new Date(),
      status: 'active',
    });

    // 更新客户的推广员工关联
    await customer.update({
      promotionEmployeeId: employee.id,
    });

    return promotion;
  }

  /**
   * 获取员工推广的客户列表
   * @param employeeId 员工ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getEmployeePromotedCustomers(
    employeeId: number,
    page = 1,
    pageSize = 10
  ) {
    const offset = (page - 1) * pageSize;
    
    return await this.findAll({
      query: { 
        employeeId,
        status: 'active'
      },
      offset,
      limit: pageSize,
      order: [['promotionTime', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: [
            'id',
            'nickname',
            'phone',
            'avatar',
            'memberStatus',
            'points',
            'createdAt',
          ],
        },
      ],
    });
  }

  /**
   * 获取客户的推广员工信息
   * @param customerId 客户ID
   */
  async getCustomerPromotionEmployee(customerId: number) {
    const promotion = await this.findOne({
      where: { 
        customerId,
        status: 'active'
      },
      include: [
        {
          model: Employee,
          attributes: [
            'id',
            'name',
            'phone',
            'avatar',
            'level',
            'rating',
            'promotionCode',
          ],
        },
      ],
    });

    return promotion;
  }

  /**
   * 获取推广统计信息
   * @param employeeId 员工ID
   */
  async getPromotionStatistics(employeeId: number) {
    const totalPromoted = await this.count({
      where: { 
        employeeId,
        status: 'active'
      },
    });

    // 获取本月推广数量
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    const monthlyPromoted = await this.count({
      where: {
        employeeId,
        status: 'active',
        promotionTime: {
          [Op.between]: [startOfMonth, endOfMonth],
        },
      },
    });

    return {
      totalPromoted,
      monthlyPromoted,
    };
  }

  /**
   * 检查推广码是否可用
   * @param promotionCode 推广码
   */
  async checkPromotionCodeAvailable(promotionCode: string) {
    const employee = await Employee.findOne({
      where: { promotionCode },
      attributes: ['id', 'name', 'promotionCode', 'status'],
    });

    if (!employee) {
      return { available: false, message: '推广码不存在' };
    }

    if (employee.status !== 1) {
      return { available: false, message: '该员工已被禁用' };
    }

    return { 
      available: true, 
      employee: {
        id: employee.id,
        name: employee.name,
        promotionCode: employee.promotionCode,
      }
    };
  }

  /**
   * 管理端查询推广关系列表
   */
  async findPromotionRelations({
    current = 1,
    pageSize = 10,
    employeeId,
    customerId,
    promotionCode,
    status,
    startTime,
    endTime,
  }: {
    current?: number;
    pageSize?: number;
    employeeId?: number;
    customerId?: number;
    promotionCode?: string;
    status?: string;
    startTime?: string;
    endTime?: string;
  }) {
    const offset = (current - 1) * pageSize;
    const whereCondition: any = {};

    if (employeeId) whereCondition.employeeId = employeeId;
    if (customerId) whereCondition.customerId = customerId;
    if (promotionCode) whereCondition.promotionCode = promotionCode;
    if (status) whereCondition.status = status;

    if (startTime && endTime) {
      whereCondition.promotionTime = {
        [Op.between]: [new Date(startTime), new Date(endTime)],
      };
    }

    return await this.findAll({
      query: whereCondition,
      offset,
      limit: pageSize,
      order: [['promotionTime', 'DESC']],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'promotionCode'],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'memberStatus'],
        },
      ],
    });
  }
}
