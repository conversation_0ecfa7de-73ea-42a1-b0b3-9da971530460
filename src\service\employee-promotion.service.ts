import { Inject, Provide } from '@midwayjs/core';
import { Employee, Customer } from '../entity';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Provide()
export class EmployeePromotionService {
  @Inject()
  ctx: Context;

  /**
   * 用户填写推广码建立推广关系
   * @param customerId 客户ID
   * @param promotionCode 推广码
   */
  async createPromotionRelation(customerId: number, promotionCode: string) {
    // 检查客户是否存在
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      throw new CustomError('客户不存在');
    }

    // 检查客户是否已经有推广关系
    if (customer.promotionEmployeeId) {
      throw new CustomError('您已经填写过推广码，不能重复填写');
    }

    // 查找推广码对应的员工
    const employee = await Employee.findOne({
      where: { promotionCode },
    });
    if (!employee) {
      throw new CustomError('推广码不存在');
    }

    if (employee.status !== 1) {
      throw new CustomError('该员工已被禁用');
    }

    // 更新客户的推广员工关联
    await customer.update({
      promotionEmployeeId: employee.id,
    });

    return {
      customerId: customer.id,
      employeeId: employee.id,
      promotionCode: employee.promotionCode,
      employeeName: employee.name,
      promotionTime: new Date(),
    };
  }

  /**
   * 获取员工推广的客户列表
   * @param employeeId 员工ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getEmployeePromotedCustomers(
    employeeId: number,
    page = 1,
    pageSize = 10
  ) {
    const offset = (page - 1) * pageSize;

    const { rows, count } = await Customer.findAndCountAll({
      where: {
        promotionEmployeeId: employeeId
      },
      attributes: [
        'id',
        'nickname',
        'phone',
        'avatar',
        'memberStatus',
        'points',
        'createdAt',
        'updatedAt',
      ],
      offset,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
    });

    return {
      list: rows,
      total: count,
      current: page,
      pageSize,
    };
  }

  /**
   * 获取客户的推广员工信息
   * @param customerId 客户ID
   */
  async getCustomerPromotionEmployee(customerId: number) {
    const customer = await Customer.findByPk(customerId, {
      include: [
        {
          model: Employee,
          as: 'promotionEmployee',
          attributes: [
            'id',
            'name',
            'phone',
            'avatar',
            'level',
            'rating',
            'promotionCode',
          ],
        },
      ],
    });

    if (!customer || !customer.promotionEmployee) {
      return null;
    }

    return {
      customerId: customer.id,
      employee: customer.promotionEmployee,
    };
  }

  /**
   * 获取推广统计信息
   * @param employeeId 员工ID
   */
  async getPromotionStatistics(employeeId: number) {
    const totalPromoted = await Customer.count({
      where: {
        promotionEmployeeId: employeeId
      },
    });

    // 获取本月推广数量
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    const monthlyPromoted = await Customer.count({
      where: {
        promotionEmployeeId: employeeId,
        updatedAt: {
          [Op.between]: [startOfMonth, endOfMonth],
        },
      },
    });

    return {
      totalPromoted,
      monthlyPromoted,
    };
  }

  /**
   * 检查推广码是否可用
   * @param promotionCode 推广码
   */
  async checkPromotionCodeAvailable(promotionCode: string) {
    const employee = await Employee.findOne({
      where: { promotionCode },
      attributes: ['id', 'name', 'promotionCode', 'status'],
    });

    if (!employee) {
      return { available: false, message: '推广码不存在' };
    }

    if (employee.status !== 1) {
      return { available: false, message: '该员工已被禁用' };
    }

    return { 
      available: true, 
      employee: {
        id: employee.id,
        name: employee.name,
        promotionCode: employee.promotionCode,
      }
    };
  }

  /**
   * 管理端查询推广关系列表
   */
  async findPromotionRelations({
    current = 1,
    pageSize = 10,
    employeeId,
    keyword,
    startTime,
    endTime,
  }: {
    current?: number;
    pageSize?: number;
    employeeId?: number;
    keyword?: string;
    startTime?: string;
    endTime?: string;
  }) {
    const offset = (current - 1) * pageSize;
    const whereCondition: any = {
      promotionEmployeeId: {
        [Op.ne]: null,
      },
    };

    if (employeeId) {
      whereCondition.promotionEmployeeId = employeeId;
    }

    if (startTime && endTime) {
      whereCondition.updatedAt = {
        [Op.between]: [new Date(startTime), new Date(endTime)],
      };
    }

    const includeOptions: any[] = [
      {
        model: Employee,
        as: 'promotionEmployee',
        attributes: ['id', 'name', 'phone', 'promotionCode'],
        where: keyword ? {
          [Op.or]: [
            { name: { [Op.like]: `%${keyword}%` } },
            { phone: { [Op.like]: `%${keyword}%` } },
            { promotionCode: { [Op.like]: `%${keyword}%` } },
          ],
        } : undefined,
        required: !!keyword,
      },
    ];

    const { rows, count } = await Customer.findAndCountAll({
      where: whereCondition,
      attributes: ['id', 'nickname', 'phone', 'memberStatus', 'promotionEmployeeId', 'updatedAt'],
      offset,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: includeOptions,
    });

    return {
      list: rows,
      total: count,
      current,
      pageSize,
    };
  }
}
