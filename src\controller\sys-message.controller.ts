import {
  Controller,
  Get,
  Post,
  Del,
  Param,
  Query,
  Body,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { MessageService } from '../service/sys-message.service';
import {
  GetMessageListDTO,
  CreateMessageDTO,
  CreateMessageFromTemplateDTO,
} from '../dto/message.dto';

@Controller('/api/message')
export class MessageController {
  @Inject()
  messageService: MessageService;

  @Get('/list/:userId')
  @Validate()
  async getMessageList(
    @Param('userId') userId: number,
    @Query() query: GetMessageListDTO
  ) {
    const { type, page = 1, limit = 20 } = query;
    return await this.messageService.getUserMessages(userId, type, page, limit);
  }

  @Get('/detail/:messageId')
  async getMessageDetail(@Param('messageId') messageId: number) {
    return await this.messageService.getMessageDetail(messageId);
  }

  @Post('/mark-read/:messageId')
  async markAsRead(@Param('messageId') messageId: number) {
    return await this.messageService.markAsRead(messageId);
  }

  @Post('/mark-all-read/:userId')
  async markAllAsRead(@Param('userId') userId: number) {
    return await this.messageService.markAllAsRead(userId);
  }

  @Del('/delete/:messageId')
  async deleteMessage(@Param('messageId') messageId: number) {
    return await this.messageService.deleteMessage(messageId);
  }

  @Get('/unread-count/:userId')
  async getUnreadCount(@Param('userId') userId: number) {
    return await this.messageService.getUnreadCount(userId);
  }

  @Post('/create')
  @Validate()
  async createMessage(@Body() body: CreateMessageDTO) {
    return await this.messageService.createMessage(body);
  }

  @Post('/create-from-template')
  @Validate()
  async createMessageFromTemplate(@Body() body: CreateMessageFromTemplateDTO) {
    return await this.messageService.createMessageFromTemplate(
      body.templateCode,
      body.userId,
      body.variables
    );
  }
}
