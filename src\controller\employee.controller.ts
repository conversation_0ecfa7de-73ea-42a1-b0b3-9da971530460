import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { EmployeeService } from '../service/employee.service';
import { CustomError } from '../error/custom.error';
import { Includeable, Op } from 'sequelize';
import { Order, OrderDetail } from '../entity';

@Controller('/employees')
export class EmployeeController {
  @Inject()
  ctx: Context;

  @Inject()
  service: EmployeeService;

  @Get('/', { summary: '查询员工列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      includeOrders,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // name和phone支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    if (queryInfo.phone) {
      queryInfo.phone = {
        [Op.like]: `%${queryInfo.phone}%`,
      };
    }

    // 基础查询只包含车辆信息，提高性能
    const include: Includeable[] = ['vehicle'];

    // 只有明确需要订单信息时才加载（通过查询参数控制）
    if (includeOrders === 'true') {
      include.push({
        model: Order,
        // 限制订单数量，避免加载过多数据
        limit: 10,
        order: [['orderTime', 'DESC']],
        include: [
          {
            model: OrderDetail,
            // 使用冗余字段，不再需要级联查询 Service
            attributes: [
              'id',
              'serviceId',
              'serviceName',
              'servicePrice',
              'petId',
              'petName',
              'petType',
              'petBreed',
              'orderTime',
              'status',
            ],
          },
        ],
      });
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include,
    });
  }

  @Get('/:id', { summary: '按ID查询员工' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定员工');
    }
    return res;
  }

  @Post('/', { summary: '新增员工' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新员工' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update(
      { id },
      { ...body, vehicleId: body.vehicleId || null }
    );
    return true;
  }

  @Del('/:id', { summary: '删除员工' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Put('/:id/wallet', { summary: '更新员工钱包余额' })
  async updateWallet(
    @Param('id') id: number,
    @Body() { amount }: { amount: number }
  ) {
    await this.service.updateWallet(id, amount);
    return true;
  }

  @Put('/:id/vehicle', { summary: '分配车辆' })
  async assignVehicle(
    @Param('id') id: number,
    @Body() { vehicleId }: { vehicleId: number }
  ) {
    await this.service.assignVehicle(id, vehicleId);
    return true;
  }

  @Get('/:id/vehicle', { summary: '获取员工的车辆' })
  async getVehicle(@Param('id') id: number) {
    return await this.service.getVehicle(id);
  }

  @Get('/:id/orders', { summary: '获取员工的订单列表' })
  async getOrders(@Param('id') id: number) {
    return await this.service.getOrders(id);
  }
}
