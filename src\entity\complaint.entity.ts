import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';
import { Order } from './order.entity';
import { Employee } from './employee.entity';

export interface ComplaintAttributes {
  /** 投诉建议ID */
  id: number;
  /** 关联用户ID（员工建议时可为null） */
  customerId?: number;
  /** 关联订单ID */
  orderId?: number;
  /** 关联员工ID（人员投诉时使用，员工建议时必填） */
  employeeId?: number;
  /** 大类：complaint-投诉，suggestion-建议 */
  category: 'complaint' | 'suggestion';
  /** 小类：order-订单，employee-人员，platform-平台，service-服务 */
  subCategory: 'order' | 'employee' | 'platform' | 'service' | 'workflow';
  /** 投诉建议标题 */
  title: string;
  /** 投诉建议内容 */
  content: string;
  /** 联系方式 */
  contactInfo?: string;
  /** 图片URL数组 */
  photoURLs?: string[];
  /** 处理状态：pending-待处理，processing-处理中，resolved-已解决，closed-已关闭 */
  status: 'pending' | 'processing' | 'resolved' | 'closed';
  /** 处理结果 */
  result?: string;
  /** 处理人员ID */
  handlerId?: number;
  /** 处理时间 */
  handledAt?: Date;
  /** 关联的用户信息 */
  customer?: Customer;
  /** 关联的订单信息 */
  order?: Order;
  /** 关联的员工信息 */
  employee?: Employee;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({ tableName: 'complaints', timestamps: true, comment: '投诉建议表' })
export class Complaint
  extends Model<ComplaintAttributes>
  implements ComplaintAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '投诉建议ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '关联用户ID（员工建议时可为null）',
  })
  @ForeignKey(() => Customer)
  customerId?: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId?: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联员工ID',
  })
  @ForeignKey(() => Employee)
  employeeId?: number;

  @Column({
    type: DataType.ENUM('complaint', 'suggestion'),
    allowNull: false,
    comment: '大类：complaint-投诉，suggestion-建议',
  })
  category: 'complaint' | 'suggestion';

  @Column({
    type: DataType.ENUM('order', 'employee', 'platform', 'service', 'workflow'),
    allowNull: false,
    comment: '小类：order-订单，employee-人员，platform-平台，service-服务',
  })
  subCategory: 'order' | 'employee' | 'platform' | 'service' | 'workflow';

  @Column({
    type: DataType.STRING(200),
    allowNull: false,
    comment: '投诉建议标题',
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '投诉建议内容',
  })
  content: string;

  @Column({
    type: DataType.STRING(100),
    comment: '联系方式',
  })
  contactInfo?: string;

  @Column({
    type: DataType.JSON,
    comment: '图片URL数组',
  })
  photoURLs?: string[];

  @Column({
    type: DataType.ENUM('pending', 'processing', 'resolved', 'closed'),
    defaultValue: 'pending',
    comment:
      '处理状态：pending-待处理，processing-处理中，resolved-已解决，closed-已关闭',
  })
  status: 'pending' | 'processing' | 'resolved' | 'closed';

  @Column({
    type: DataType.TEXT,
    comment: '处理结果',
  })
  result?: string;

  @Column({
    type: DataType.INTEGER,
    comment: '处理人员ID',
  })
  handlerId?: number;

  @Column({
    type: DataType.DATE,
    comment: '处理时间',
  })
  handledAt?: Date;

  @BelongsTo(() => Customer)
  customer: Customer;

  @BelongsTo(() => Order, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  order: Order;

  @BelongsTo(() => Employee, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  employee: Employee;
}
