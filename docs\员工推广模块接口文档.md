# 员工推广模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 业务规则说明

1. **推广关系唯一性**：每个用户只能被一个员工推广，一旦建立推广关系不可更改
2. **推广码唯一性**：每个员工都有唯一的推广码，系统自动生成
3. **填写限制**：用户只有一次填写推广码的机会
4. **员工状态**：只有启用状态的员工推广码才有效

---

## 1. 用户端接口

### 1.1 检查推广码是否可用
**接口地址：** `GET /employee-promotion/check-code/{promotionCode}`  
**接口描述：** 检查推广码是否存在且有效  
**是否需要认证：** 否  
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| promotionCode | string | 是 | 推广码 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "available": true,
    "employee": {
      "id": 1,
      "name": "张师傅",
      "promotionCode": "ABC12345"
    }
  }
}
```

**错误响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "available": false,
    "message": "推广码不存在"
  }
}
```

### 1.2 填写推广码建立推广关系
**接口地址：** `POST /employee-promotion/create`  
**接口描述：** 用户填写推广码建立与员工的推广关系  
**是否需要认证：** 是  
**适用端：** 用户端

**请求体：**
```json
{
  "customerId": 1,
  "promotionCode": "ABC12345"
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| promotionCode | string | 是 | 推广码 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "customerId": 1,
    "employeeId": 1,
    "promotionCode": "ABC12345",
    "employeeName": "张师傅",
    "promotionTime": "2024-01-01T10:00:00.000Z"
  }
}
```

### 1.3 查看关联的推广员工
**接口地址：** `GET /employee-promotion/customer/{customerId}/employee`  
**接口描述：** 用户查看自己关联的推广员工信息  
**是否需要认证：** 是  
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "customerId": 1,
    "employee": {
      "id": 1,
      "name": "张师傅",
      "phone": "13800138001",
      "avatar": "https://example.com/avatar1.jpg",
      "level": 5,
      "rating": 4.8,
      "promotionCode": "ABC12345"
    }
  }
}
```

**无推广关系响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": null
}
```

---

## 2. 员工端接口

### 2.1 查看推广的客户列表
**接口地址：** `GET /employee-promotion/employee/{employeeId}/customers`  
**接口描述：** 员工查看自己推广的客户列表  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "小王",
        "phone": "13800138001",
        "avatar": "https://example.com/avatar1.jpg",
        "memberStatus": 1,
        "points": 100,
        "createdAt": "2024-01-01T10:00:00.000Z",
        "updatedAt": "2024-01-01T10:00:00.000Z"
      }
    ],
    "total": 15,
    "current": 1,
    "pageSize": 10
  }
}
```

### 2.2 查看推广统计信息
**接口地址：** `GET /employee-promotion/employee/{employeeId}/statistics`  
**接口描述：** 员工查看推广统计数据  
**是否需要认证：** 是  
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalPromoted": 25,
    "monthlyPromoted": 5
  }
}
```

---

## 3. 管理端接口

### 3.1 查询推广关系列表
**接口地址：** `GET /employee-promotion`  
**接口描述：** 管理端查询推广关系列表，支持分页和筛选  
**是否需要认证：** 是  
**适用端：** 管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| employeeId | number | 否 | 员工ID筛选 |
| keyword | string | 否 | 关键词搜索（员工姓名、手机号、推广码） |
| startTime | string | 否 | 开始时间（ISO格式） |
| endTime | string | 否 | 结束时间（ISO格式） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "小王",
        "phone": "13800138001",
        "memberStatus": 1,
        "promotionEmployeeId": 1,
        "updatedAt": "2024-01-01T10:00:00.000Z",
        "promotionEmployee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001",
          "promotionCode": "ABC12345"
        }
      }
    ],
    "total": 50,
    "current": 1,
    "pageSize": 10
  }
}
```

## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录已过期 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 5. 常见错误信息

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 客户ID和推广码不能为空 | 必填参数缺失 | 检查请求参数 |
| 您已经填写过推广码，不能重复填写 | 用户已有推广关系 | 提示用户已绑定员工 |
| 推广码不存在 | 推广码无效 | 检查推广码是否正确 |
| 该员工已被禁用 | 员工状态异常 | 联系管理员 |
| 客户不存在 | 客户ID无效 | 检查客户ID |

## 6. 注意事项

1. **唯一性约束**：每个用户只能填写一次推广码，建立推广关系后不可更改
2. **员工状态检查**：只有启用状态的员工推广码才有效
3. **推广码格式**：推广码为8位字符，包含数字和字母
4. **权限控制**：用户只能查看自己的推广信息，员工只能查看自己推广的客户
5. **数据关联**：推广关系通过客户表的promotionEmployeeId字段维护
6. **统计数据**：月度统计基于客户记录的更新时间计算
7. **搜索功能**：管理端支持按员工信息关键词搜索推广关系
8. **分页查询**：所有列表接口都支持分页，默认每页10条记录
