import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/controller/employee-promotion.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup', err);
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should GET /employee-promotion/check-code/:promotionCode', async () => {
    // 测试检查推广码
    const result = await createHttpRequest(app)
      .get('/employee-promotion/check-code/TEST123')
      .expect(200);

    console.log('检查推广码结果:', result.body);
    expect(result.body.errCode).toBe(0);
  });

  it('should POST /employee-promotion/create', async () => {
    // 测试创建推广关系
    const result = await createHttpRequest(app)
      .post('/employee-promotion/create')
      .send({
        customerId: 1,
        promotionCode: 'TEST123'
      })
      .expect(200);

    console.log('创建推广关系结果:', result.body);
    expect(result.body.errCode).toBe(0);
  });

  it('should GET /employee-promotion/customer/:customerId/employee', async () => {
    // 测试获取客户的推广员工
    const result = await createHttpRequest(app)
      .get('/employee-promotion/customer/1/employee')
      .expect(200);

    console.log('获取客户推广员工结果:', result.body);
    expect(result.body.errCode).toBe(0);
  });

  it('should GET /employee-promotion/employee/:employeeId/customers', async () => {
    // 测试获取员工推广的客户列表
    const result = await createHttpRequest(app)
      .get('/employee-promotion/employee/1/customers')
      .query({ current: 1, pageSize: 10 })
      .expect(200);

    console.log('获取员工推广客户列表结果:', result.body);
    expect(result.body.errCode).toBe(0);
  });

  it('should GET /employee-promotion/employee/:employeeId/statistics', async () => {
    // 测试获取员工推广统计
    const result = await createHttpRequest(app)
      .get('/employee-promotion/employee/1/statistics')
      .expect(200);

    console.log('获取员工推广统计结果:', result.body);
    expect(result.body.errCode).toBe(0);
  });

  it('should GET /employee-promotion', async () => {
    // 测试管理端查询推广关系列表
    const result = await createHttpRequest(app)
      .get('/employee-promotion')
      .query({ current: 1, pageSize: 10 })
      .expect(200);

    console.log('管理端查询推广关系列表结果:', result.body);
    expect(result.body.errCode).toBe(0);
  });
});
