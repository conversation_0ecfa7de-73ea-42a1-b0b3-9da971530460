import { Table, Column, Model, DataType, HasOne } from 'sequelize-typescript';
import { Employee } from './employee.entity';

export interface VehicleAttributes {
  /** 车辆ID */
  id: number;
  /** 车牌号 */
  plateNumber: string;
  /** 车辆类型 */
  vehicleType?: string;
  /** 实时纬度 */
  latitude?: number;
  /** 实时经度 */
  longitude?: number;
  /** 车辆状态（空闲/服务中） */
  status: string;
  /** 关联的员工信息 */
  employee?: Employee;
}

@Table({ tableName: 'vehicles', timestamps: true, comment: '车辆表' })
export class Vehicle
  extends Model<VehicleAttributes>
  implements VehicleAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '车辆ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    unique: {
      name: 'plateNumber',
      msg: '已存在相同的车牌号',
    },
    allowNull: false,
    comment: '车牌号',
  })
  plateNumber: string;

  @Column({
    type: DataType.STRING(20),
    comment: '车辆类型',
  })
  vehicleType: string;

  @Column({
    type: DataType.DECIMAL(9, 6),
    comment: '实时纬度',
  })
  latitude: number;

  @Column({
    type: DataType.DECIMAL(9, 6),
    comment: '实时经度',
  })
  longitude: number;

  @Column({
    type: DataType.STRING(20),
    defaultValue: '空闲',
    comment: '状态（空闲/服务中）',
  })
  status: string;

  @HasOne(() => Employee)
  employee: Employee;
}
