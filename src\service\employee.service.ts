import { Provide } from '@midwayjs/core';
import { Employee } from '../entity/employee.entity';
import { BaseService } from '../common/BaseService';
import { Vehicle } from '../entity/vehicle.entity';
import { Order } from '../entity/order.entity';
import { Review } from '../entity/review.entity';

@Provide()
export class EmployeeService extends BaseService<Employee> {
  constructor() {
    super('员工');
  }

  getModel() {
    return Employee;
  }

  async findByPhone(phone: string) {
    return await this.findOne({ where: { phone } });
  }

  async updateWallet(id: number, amount: number) {
    const employee = await this.findById(id);
    if (!employee) {
      throw new Error('员工不存在');
    }
    return await employee.update({
      walletBalance: employee.walletBalance + amount,
    });
  }

  async assignVehicle(id: number, vehicleId: number) {
    return await this.update({ id }, { vehicleId });
  }

  async getVehicle(employeeId: number) {
    const employee = await Employee.findByPk(employeeId, {
      include: [Vehicle],
    });
    return employee?.vehicle;
  }

  /**
   * 更新员工服务评分
   * @param employeeId 员工ID
   */
  async updateEmployeeRating(employeeId: number) {
    // 查询该员工所有订单的评价
    const reviews = await Review.findAll({
      include: [
        {
          model: Order,
          required: true,
          where: {
            employeeId: employeeId,
          },
          attributes: [], // 不需要返回订单字段
        },
      ],
      attributes: ['rating'],
    });

    let averageRating = null;
    const reviewCount = reviews.length;

    if (reviewCount > 0) {
      // 计算所有评价星数的平均值
      const totalRating = reviews.reduce(
        (sum, review) => sum + review.rating,
        0
      );
      averageRating = Number((totalRating / reviewCount).toFixed(2));
    }

    // 更新员工评分
    await this.update({ id: employeeId }, { rating: averageRating });

    return {
      employeeId,
      averageRating,
      reviewCount,
    };
  }

  async getOrders(employeeId: number) {
    const employee = await Employee.findByPk(employeeId, {
      include: [Order],
    });
    return employee?.orders || [];
  }
}
