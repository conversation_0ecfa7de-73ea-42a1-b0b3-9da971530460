import { ILogger, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { SysMessage, MessageTemplate } from '../entity';
import { Op } from 'sequelize';

@Provide()
export class MessageStatisticsService {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  /**
   * 获取消息统计概览
   */
  async getMessageOverview() {
    // 总消息数
    const totalMessages = await SysMessage.count();

    // 未读消息数
    const unreadMessages = await SysMessage.count({
      where: { isRead: false },
    });

    // 按类型统计
    const messagesByType = await SysMessage.findAll({
      attributes: [
        'type',
        [
          SysMessage.sequelize.fn('COUNT', SysMessage.sequelize.col('id')),
          'count',
        ],
      ],
      group: ['type'],
      raw: true,
    });

    // 今日新增消息
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayMessages = await SysMessage.count({
      where: {
        createdAt: {
          [Op.gte]: today,
        },
      },
    });

    // 本周新增消息
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const weekMessages = await SysMessage.count({
      where: {
        createdAt: {
          [Op.gte]: weekStart,
        },
      },
    });

    return {
      totalMessages,
      unreadMessages,
      readMessages: totalMessages - unreadMessages,
      messagesByType: this.formatTypeStatistics(messagesByType),
      todayMessages,
      weekMessages,
    };
  }

  /**
   * 获取用户消息统计
   */
  async getUserMessageStatistics(userId: number) {
    // 用户总消息数
    const totalMessages = await SysMessage.count({
      where: { userId },
    });

    // 用户未读消息数
    const unreadMessages = await SysMessage.count({
      where: { userId, isRead: false },
    });

    // 用户按类型统计
    const messagesByType = await SysMessage.findAll({
      attributes: [
        'type',
        [
          SysMessage.sequelize.fn('COUNT', SysMessage.sequelize.col('id')),
          'count',
        ],
      ],
      where: { userId },
      group: ['type'],
      raw: true,
    });

    // 最近7天消息趋势
    const weeklyTrend = await this.getWeeklyMessageTrend(userId);

    return {
      userId,
      totalMessages,
      unreadMessages,
      readMessages: totalMessages - unreadMessages,
      messagesByType: this.formatTypeStatistics(messagesByType),
      weeklyTrend,
    };
  }

  /**
   * 获取消息模板使用统计
   */
  async getTemplateUsageStatistics() {
    // 获取所有模板
    const templates = await MessageTemplate.findAll({
      attributes: ['id', 'code', 'title', 'type'],
    });

    // 统计每个模板的使用次数（这里需要在消息表中添加templateId字段，或者通过其他方式关联）
    // 暂时返回模板列表
    return {
      templates: templates.map(template => ({
        id: template.id,
        code: template.code,
        title: template.title,
        type: template.type,
        usageCount: 0, // 需要实现具体的统计逻辑
      })),
    };
  }

  /**
   * 获取最近7天的消息趋势
   */
  private async getWeeklyMessageTrend(userId?: number) {
    const trends = [];
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);

      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const whereCondition: any = {
        createdAt: {
          [Op.gte]: date,
          [Op.lt]: nextDate,
        },
      };

      if (userId) {
        whereCondition.userId = userId;
      }

      const count = await SysMessage.count({
        where: whereCondition,
      });

      trends.push({
        date: date.toISOString().split('T')[0],
        count,
      });
    }

    return trends;
  }

  /**
   * 格式化类型统计数据
   */
  private formatTypeStatistics(typeStats: any[]) {
    const result = {
      system: 0,
      platform: 0,
      order: 0,
    };

    typeStats.forEach((item: any) => {
      result[item.type] = parseInt(item.count);
    });

    return result;
  }

  /**
   * 获取消息阅读率统计
   */
  async getReadRateStatistics(days = 7) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);

    const totalMessages = await SysMessage.count({
      where: {
        createdAt: {
          [Op.gte]: startDate,
        },
      },
    });

    const readMessages = await SysMessage.count({
      where: {
        createdAt: {
          [Op.gte]: startDate,
        },
        isRead: true,
      },
    });

    const readRate =
      totalMessages > 0
        ? ((readMessages / totalMessages) * 100).toFixed(2)
        : '0.00';

    return {
      period: `最近${days}天`,
      totalMessages,
      readMessages,
      unreadMessages: totalMessages - readMessages,
      readRate: parseFloat(readRate),
    };
  }
}
