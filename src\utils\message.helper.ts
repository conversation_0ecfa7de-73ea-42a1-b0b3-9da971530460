import { Provide, Inject, ILogger } from '@midwayjs/core';
import { MessageService } from '../service/sys-message.service';
import { MESSAGE_CONSTANTS } from '../common/Constant';

/**
 * 消息助手类 - 提供便捷的消息发送方法
 */
@Provide()
export class MessageHelper {
  @Inject()
  messageService: MessageService;

  @Inject()
  logger: ILogger;

  /**
   * 发送新订单通知
   */
  async sendNewOrderNotification(
    userId: number,
    orderData: {
      orderNo: string;
      customerName: string;
      serviceTime?: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_NEW,
        userId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单新消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送订单接单通知
   */
  async sendOrderAcceptedNotification(
    userId: number,
    orderData: {
      orderNo: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_ACCEPTED,
        userId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单接单消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送订单完成通知
   */
  async sendOrderCompletedNotification(
    userId: number,
    orderData: {
      orderNo: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_COMPLETED,
        userId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单完成消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送系统维护通知
   */
  async sendSystemMaintenanceNotification(
    userId: number,
    maintenanceData: {
      maintenanceTime: string;
      duration: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.SYSTEM_MAINTENANCE,
        userId,
        maintenanceData
      );
    } catch (error) {
      this.logger.error(
        `系统维护消息通知发送失败：${JSON.stringify(maintenanceData)}`,
        error
      );
    }
  }

  /**
   * 发送平台政策更新通知
   */
  async sendPlatformPolicyNotification(userId: number) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.PLATFORM_POLICY,
        userId,
        {}
      );
    } catch (error) {
      this.logger.error(`平台政策更新消息通知发送失败: ${userId}`, error);
    }
  }

  /**
   * 发送自定义消息
   */
  async sendCustomMessage(
    userId: number,
    type: 'system' | 'platform' | 'order',
    title: string,
    content: string,
    extraData?: any
  ) {
    try {
      await this.messageService.createMessage({
        userId,
        type,
        title,
        content,
        extraData,
      });
    } catch (error) {
      this.logger.error(
        `发送自定义消息失败：${JSON.stringify({
          userId,
          type,
          title,
          content,
          extraData,
        })}`,
        error
      );
    }
  }

  /**
   * 批量发送消息给多个用户
   */
  async sendBatchMessage(
    userIds: number[],
    type: 'system' | 'platform' | 'order',
    title: string,
    content: string,
    extraData?: any
  ) {
    const promises = userIds.map(userId =>
      this.sendCustomMessage(userId, type, title, content, extraData)
    );
    return await Promise.all(promises);
  }

  /**
   * 通过模板批量发送消息
   */
  async sendBatchMessageFromTemplate(
    userIds: number[],
    templateCode: string,
    variables: Record<string, any> = {}
  ) {
    const promises = userIds.map(userId =>
      this.messageService.createMessageFromTemplate(
        templateCode,
        userId,
        variables
      )
    );
    return await Promise.all(promises);
  }
}
