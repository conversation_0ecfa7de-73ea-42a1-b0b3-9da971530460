import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Employee } from './employee.entity';
import { Customer } from './customer.entity';

export interface EmployeePromotionAttributes {
  /** 推广记录ID */
  id: number;
  /** 员工ID */
  employeeId: number;
  /** 客户ID */
  customerId: number;
  /** 推广码 */
  promotionCode: string;
  /** 推广时间 */
  promotionTime: Date;
  /** 状态：active-有效 inactive-无效 */
  status: string;
  /** 关联的员工信息 */
  employee?: Employee;
  /** 关联的客户信息 */
  customer?: Customer;
}

@Table({
  tableName: 'employee_promotions',
  timestamps: true,
  indexes: [
    {
      name: 'employee_customer_unique',
      fields: ['employeeId', 'customerId'],
      unique: true,
    },
    {
      name: 'customer_unique',
      fields: ['customerId'],
      unique: true,
    },
  ],
  comment: '员工推广关系表',
})
export class EmployeePromotion
  extends Model<EmployeePromotionAttributes>
  implements EmployeePromotionAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '推广记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '员工ID',
  })
  @ForeignKey(() => Employee)
  employeeId: number;

  @BelongsTo(() => Employee, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  employee: Employee;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '客户ID',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @BelongsTo(() => Customer, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  customer: Customer;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '推广码',
  })
  promotionCode: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '推广时间',
  })
  promotionTime: Date;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    defaultValue: 'active',
    comment: '状态：active-有效 inactive-无效',
  })
  status: string;
}
