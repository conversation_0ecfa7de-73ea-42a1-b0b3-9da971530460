import { Provide, Inject, Logger, <PERSON>ope, <PERSON>opeEnum } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { WebSocketService } from './websocket.service';

/**
 * 消息广播服务
 * 统一处理各种业务消息的广播
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class MessageBroadcastService {
  @Inject()
  webSocketService: WebSocketService;

  @Logger()
  logger: ILogger;

  /**
   * 广播新订单消息
   * @param orderId 订单ID
   */
  async broadcastNewOrder(orderId: number) {
    try {
      const message = {
        type: 'new_order',
        data: {
          orderId,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(message);
      this.logger.info(`成功广播新订单消息：订单ID ${orderId}`);
    } catch (error) {
      this.logger.error(`广播新订单消息失败：订单ID ${orderId}`, error);
    }
  }

  /**
   * 广播取消订单消息
   * @param orderId 订单ID
   */
  async broadcastCancelOrder(orderId: number) {
    try {
      const message = {
        type: 'cancel_order',
        data: {
          orderId,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(message);
      this.logger.info(`成功广播取消订单消息：订单ID ${orderId}`);
    } catch (error) {
      this.logger.error(`广播取消订单消息失败：订单ID ${orderId}`, error);
    }
  }

  /**
   * 广播订单状态变更消息
   * @param orderId 订单ID
   * @param status 新状态
   */
  async broadcastOrderStatusChange(orderId: number, status: string) {
    try {
      const message = {
        type: 'order_status_change',
        data: {
          orderId,
          status,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(message);
      this.logger.info(
        `成功广播订单状态变更消息：订单ID ${orderId}, 状态 ${status}`
      );
    } catch (error) {
      this.logger.error(`广播订单状态变更消息失败：订单ID ${orderId}`, error);
    }
  }

  /**
   * 广播系统通知消息
   * @param message 通知内容
   * @param level 通知级别
   */
  async broadcastSystemNotification(
    message: string,
    level: 'info' | 'warning' | 'error' = 'info'
  ) {
    try {
      const notification = {
        type: 'system_notification',
        data: {
          message,
          level,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(notification);
      this.logger.info(`成功广播系统通知：${message}`);
    } catch (error) {
      this.logger.error(`广播系统通知失败：${message}`, error);
    }
  }
}
